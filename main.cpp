#include "mainwindow.h"

#include <QApplication>

// 前向声明
void logSDKOutput(const QString& message);
#include <QCoreApplication>
#include <QDir>
#include <QStandardPaths>
#include <QDateTime>
#include <QDebug>
#include <QTextStream>
#include <QFile>
#include <QMutex>

// 全局日志文件变量
static QFile* g_logFile = nullptr;
static QTextStream* g_logStream = nullptr;
static QMutex g_logMutex;

// 写入日志到文件的通用函数
void writeToLogFile(const QString& message)
{
    QMutexLocker locker(&g_logMutex);
    if (g_logStream) {
        *g_logStream << message << Qt::endl;
        g_logStream->flush();
    }
}

// 自定义日志处理函数
void messageOutput(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString typeStr;

    switch (type) {
    case QtDebugMsg:    typeStr = "DEBUG"; break;
    case QtWarningMsg:  typeStr = "WARN "; break;
    case QtCriticalMsg: typeStr = "ERROR"; break;
    case QtFatalMsg:    typeStr = "FATAL"; break;
    case QtInfoMsg:     typeStr = "INFO "; break;
    }

    QString formattedMsg = QString("[%1] [%2] %3").arg(timestamp, typeStr, msg);

    // 输出到控制台（开发时可见）
    fprintf(stderr, "%s\n", formattedMsg.toLocal8Bit().constData());

    // 写入日志文件
    writeToLogFile(formattedMsg);
}

// 初始化日志系统
void initializeLogging()
{
    // 创建日志目录（在应用程序目录下）
    QString appDir = QCoreApplication::applicationDirPath();
    QString logDir = appDir + "/logs";

    // 确保logs目录存在
    QDir dir;
    if (!dir.exists(logDir)) {
        if (!dir.mkpath(logDir)) {
            qWarning() << "Failed to create log directory:" << logDir;
            return;
        }
    }

    // 创建日志文件（按日期命名）
    QString logFileName = QString("idcamera_%1.log").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd"));
    QString logFilePath = logDir + "/" + logFileName;

    g_logFile = new QFile(logFilePath);
    if (g_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        g_logStream = new QTextStream(g_logFile);
        g_logStream->setEncoding(QStringConverter::Utf8);

        qDebug() << "Log system initialized. Log file:" << logFilePath;
        qDebug() << "Application directory:" << appDir;
        qDebug() << "Log directory:" << logDir;
        qDebug() << "Application started at" << QDateTime::currentDateTime().toString();
    } else {
        qWarning() << "Failed to open log file:" << logFilePath;
        qWarning() << "Please check if the application has write permissions to:" << logDir;
    }
}

// SDK输出捕获函数（简化版本）
void logSDKOutput(const QString& message)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString formattedMsg = QString("[%1] [SDK  ] %2").arg(timestamp, message);

    // 输出到控制台（开发时可见）
    fprintf(stderr, "%s\n", formattedMsg.toLocal8Bit().constData());

    // 写入日志文件
    writeToLogFile(formattedMsg);
}

// 清理日志系统
void cleanupLogging()
{
    if (g_logStream) {
        qDebug() << "Application ended at" << QDateTime::currentDateTime().toString();
        delete g_logStream;
        g_logStream = nullptr;
    }

    if (g_logFile) {
        g_logFile->close();
        delete g_logFile;
        g_logFile = nullptr;
    }
}

int main(int argc, char *argv[])
{
    // 尝试禁用硬件加速来解决图形驱动相关的段错误
    QApplication::setAttribute(Qt::AA_UseSoftwareOpenGL, true);

    // 设置平台插件为软件渲染
    qputenv("QT_QPA_PLATFORM", "windows:gl=software");

    QApplication a(argc, argv);

    // 设置应用程序信息（用于日志路径）
    a.setApplicationName("IDCamera");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("YourCompany");

    // 初始化日志系统
    initializeLogging();
    qInstallMessageHandler(messageOutput);

    // Qt 6 默认使用UTF-8编码，支持中文，无需额外设置
    MainWindow w;
    w.show();

    int result = a.exec();

    // 清理日志系统
    cleanupLogging();

    return result;
}
